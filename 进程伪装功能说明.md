# 进程伪装功能说明

## 概述

本项目新增了高级进程伪装功能，可以有效隐藏录屏程序的真实身份，绕过各种进程检测机制。

## 功能特性

### 🎭 进程伪装
- **进程名伪装**: 将程序伪装成系统进程（如 svchost.exe、explorer.exe 等）
- **命令行伪装**: 修改显示的命令行参数
- **进程路径伪装**: 伪装程序的执行路径
- **内存特征混淆**: 修改进程内存中的特征字符串

### 🔒 反检测机制
- **API Hook**: 拦截进程枚举相关的系统API
- **进程噪音**: 创建合法的后台进程来混淆检测
- **内存欺骗**: 在内存中创建假的进程信息
- **窗口隐藏**: 自动隐藏程序窗口

### 🛡️ 多层保护
- **基础伪装**: 修改进程标题和基本信息
- **高级伪装**: Hook系统API，拦截进程查询
- **自适应模式**: 根据检测到的软件调整伪装策略

## 文件结构

```
├── process_disguise.py          # 核心进程伪装模块
├── anti_detection.py           # 反检测管理器（已更新）
├── screen_recorder.py          # 主录屏程序（已集成伪装）
├── start_with_disguise.py      # 带伪装的启动器
├── test_process_disguise.py    # 伪装功能测试脚本
└── 进程伪装功能说明.md         # 本说明文档
```

## 使用方法

### 1. 基本使用

```bash
# 使用新的启动器（自动启用伪装）
python start_with_disguise.py

# 或使用原始录屏器（手动控制）
python screen_recorder.py
```

### 2. 测试伪装功能

```bash
# 运行伪装测试脚本
python test_process_disguise.py
```

### 3. 编程接口

```python
from process_disguise import ProcessDisguise
from anti_detection import AntiDetectionManager

# 基础进程伪装
disguise = ProcessDisguise()
disguise.enable_full_disguise()

# 高级反检测管理
anti_detection = AntiDetectionManager()
anti_detection.enable_all_protections()

# 获取伪装状态
status = anti_detection.get_disguise_status()
print(f"伪装为: {status['name']}")
```

## 伪装技术详解

### 1. 进程名伪装
- 修改控制台标题
- 尝试修改进程映像名（需要管理员权限）
- 从预定义的系统进程列表中随机选择

### 2. 命令行伪装
- 创建假的命令行参数
- Hook GetCommandLine API（计划中）
- 模拟系统服务的启动参数

### 3. 内存特征混淆
- 修改PE头信息
- 添加假的导入表
- 在内存中分散存储假字符串
- 创建随机内存分配来改变布局

### 4. API Hook技术
- Hook EnumProcesses API
- Hook CreateToolhelp32Snapshot API
- Hook NtQuerySystemInformation API
- Hook GetModuleFileName API

### 5. 进程噪音
- 启动合法的后台进程（如记事本）
- 立即最小化和隐藏窗口
- 创建多个假进程信息

## 安全注意事项

### ⚠️ 重要提醒
1. **合法使用**: 本功能仅用于合法的录屏需求，请勿用于恶意目的
2. **管理员权限**: 某些高级功能需要管理员权限才能生效
3. **杀毒软件**: 可能被某些杀毒软件误报，这是正常现象
4. **系统稳定性**: 过度的API Hook可能影响系统稳定性

### 🔧 故障排除
- 如果伪装失败，检查是否有足够的权限
- 某些企业级安全软件可能会阻止API Hook
- 在虚拟机中测试时效果可能有限

## 配置选项

### 系统进程列表
```python
system_processes = [
    "svchost.exe",      # 服务主机进程
    "explorer.exe",     # 资源管理器
    "winlogon.exe",     # 登录进程
    "csrss.exe",        # 客户端服务器运行时
    "dwm.exe",          # 桌面窗口管理器
    "audiodg.exe",      # 音频设备图形隔离
    "lsass.exe",        # 本地安全授权
    "services.exe",     # 服务控制管理器
]
```

### 合法应用列表
```python
legitimate_apps = [
    "notepad.exe",      # 记事本
    "calc.exe",         # 计算器
    "mspaint.exe",      # 画图
    "wordpad.exe",      # 写字板
]
```

## 性能影响

- **CPU开销**: 约增加1-3%的CPU使用率
- **内存开销**: 约增加10-20MB内存使用
- **启动时间**: 增加2-3秒的初始化时间
- **录制性能**: 对录制性能影响微乎其微

## 兼容性

### 支持的系统
- ✅ Windows 10 (1903+)
- ✅ Windows 11
- ⚠️ Windows 8.1 (部分功能)
- ❌ Windows 7 (不支持)

### 支持的Python版本
- ✅ Python 3.8+
- ✅ Python 3.9
- ✅ Python 3.10
- ✅ Python 3.11
- ✅ Python 3.12
- ✅ Python 3.13

## 更新日志

### v2.0 (当前版本)
- ✨ 新增完整的进程伪装功能
- ✨ 新增高级反检测管理器
- ✨ 新增进程噪音生成
- ✨ 新增API Hook框架
- ✨ 新增自适应伪装模式
- 🔧 优化内存使用和性能
- 🔧 改进错误处理和日志记录

### v1.0 (之前版本)
- 基础DRM绕过功能
- 简单的窗口隐藏
- 基础的进程名修改

## 技术支持

如果遇到问题，请检查：
1. Python版本是否兼容
2. 依赖包是否正确安装
3. 是否有足够的系统权限
4. 杀毒软件是否误报

## 免责声明

本软件仅供学习和合法用途使用。使用者应当遵守当地法律法规，不得将本软件用于任何非法活动。开发者不承担因使用本软件而产生的任何法律责任。
