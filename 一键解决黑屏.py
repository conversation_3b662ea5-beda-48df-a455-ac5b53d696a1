#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键解决播放器黑屏问题
自动尝试所有可能的解决方案
"""

import cv2
import time
import os
import sys
import subprocess
import numpy as np
from screen_recorder import AntiDetectionRecorder
from advanced_drm_bypass import AdvancedDRMBypass
from drm_bypass import DRMBypassCapture

class BlackScreenSolver:
    """黑屏问题解决器"""
    
    def __init__(self):
        self.recorder = AntiDetectionRecorder()
        self.advanced_bypass = AdvancedDRMBypass()
        self.standard_bypass = DRMBypassCapture()
        self.solutions_tried = []
        self.successful_solution = None
        
    def solve_black_screen(self):
        """一键解决黑屏问题"""
        print("🎯 播放器黑屏问题一键解决工具")
        print("=" * 60)
        
        # 检查运行环境
        if not self.check_environment():
            return False
        
        # 尝试所有解决方案
        solutions = [
            ("标准DRM绕过", self.try_standard_drm_bypass),
            ("高级DRM绕过", self.try_advanced_drm_bypass),
            ("GPU缓冲区捕获", self.try_gpu_buffer_capture),
            ("多进程捕获", self.try_multi_process_capture),
            ("窗口合成捕获", self.try_window_composition),
            ("系统优化方案", self.try_system_optimization),
        ]
        
        for solution_name, solution_func in solutions:
            print(f"\n🔧 尝试解决方案: {solution_name}")
            print("-" * 40)
            
            try:
                if solution_func():
                    self.successful_solution = solution_name
                    print(f"✅ 解决方案 '{solution_name}' 成功！")
                    return True
                else:
                    print(f"❌ 解决方案 '{solution_name}' 失败")
                    
            except Exception as e:
                print(f"❌ 解决方案 '{solution_name}' 出错: {e}")
            
            self.solutions_tried.append(solution_name)
        
        # 如果所有方案都失败
        print(f"\n⚠️  所有自动解决方案都失败了")
        self.show_manual_solutions()
        return False
    
    def check_environment(self):
        """检查运行环境"""
        print("🔍 检查运行环境...")
        
        # 检查管理员权限
        try:
            import ctypes
            is_admin = ctypes.windll.shell32.IsUserAnAdmin()
            if not is_admin:
                print("⚠️  建议以管理员身份运行以获得最佳效果")
                response = input("是否继续？(y/n): ").lower().strip()
                if response != 'y':
                    return False
        except:
            pass
        
        # 检查依赖库
        required_modules = ['cv2', 'numpy', 'win32gui', 'PIL']
        missing_modules = []
        
        for module in required_modules:
            try:
                __import__(module)
            except ImportError:
                missing_modules.append(module)
        
        if missing_modules:
            print(f"❌ 缺少依赖库: {', '.join(missing_modules)}")
            print("请运行: pip install opencv-python numpy pywin32 pillow")
            return False
        
        print("✅ 环境检查通过")
        return True
    
    def try_standard_drm_bypass(self):
        """尝试标准DRM绕过"""
        try:
            frame = self.standard_bypass.capture_with_drm_bypass()
            if frame is not None and not self._is_black_screen(frame):
                cv2.imwrite("solution_standard_drm.png", frame)
                print("✅ 标准DRM绕过成功，已保存测试图片")
                return True
            return False
        except Exception as e:
            print(f"标准DRM绕过失败: {e}")
            return False
    
    def try_advanced_drm_bypass(self):
        """尝试高级DRM绕过"""
        try:
            frame = self.advanced_bypass.capture_with_advanced_bypass()
            if frame is not None and not self._is_black_screen(frame):
                cv2.imwrite("solution_advanced_drm.png", frame)
                print("✅ 高级DRM绕过成功，已保存测试图片")
                return True
            return False
        except Exception as e:
            print(f"高级DRM绕过失败: {e}")
            return False
    
    def try_gpu_buffer_capture(self):
        """尝试GPU缓冲区捕获"""
        try:
            frame = self.advanced_bypass._gpu_buffer_capture()
            if frame is not None and not self._is_black_screen(frame):
                cv2.imwrite("solution_gpu_buffer.png", frame)
                print("✅ GPU缓冲区捕获成功，已保存测试图片")
                return True
            return False
        except Exception as e:
            print(f"GPU缓冲区捕获失败: {e}")
            return False
    
    def try_multi_process_capture(self):
        """尝试多进程捕获"""
        try:
            frame = self.advanced_bypass._multi_process_capture()
            if frame is not None and not self._is_black_screen(frame):
                cv2.imwrite("solution_multi_process.png", frame)
                print("✅ 多进程捕获成功，已保存测试图片")
                return True
            return False
        except Exception as e:
            print(f"多进程捕获失败: {e}")
            return False
    
    def try_window_composition(self):
        """尝试窗口合成捕获"""
        try:
            frame = self.advanced_bypass._overlay_injection()
            if frame is not None and not self._is_black_screen(frame):
                cv2.imwrite("solution_window_composition.png", frame)
                print("✅ 窗口合成捕获成功，已保存测试图片")
                return True
            return False
        except Exception as e:
            print(f"窗口合成捕获失败: {e}")
            return False
    
    def try_system_optimization(self):
        """尝试系统优化方案"""
        print("🔧 应用系统优化设置...")
        
        try:
            # 尝试禁用硬件加速GPU调度
            self._disable_hardware_acceleration()
            
            # 等待设置生效
            time.sleep(2)
            
            # 重新测试
            frame = self.recorder.capture_screen()
            if frame is not None and not self._is_black_screen(frame):
                cv2.imwrite("solution_system_optimization.png", frame)
                print("✅ 系统优化方案成功，已保存测试图片")
                return True
            
            return False
            
        except Exception as e:
            print(f"系统优化失败: {e}")
            return False
    
    def _disable_hardware_acceleration(self):
        """禁用硬件加速（需要管理员权限）"""
        try:
            import winreg
            
            # 尝试修改注册表禁用硬件加速
            key_path = r"SYSTEM\CurrentControlSet\Control\GraphicsDrivers"
            
            try:
                key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, key_path, 0, winreg.KEY_SET_VALUE)
                winreg.SetValueEx(key, "HwSchMode", 0, winreg.REG_DWORD, 1)
                winreg.CloseKey(key)
                print("✅ 已禁用硬件加速GPU调度")
            except PermissionError:
                print("⚠️  需要管理员权限来修改系统设置")
            except Exception as e:
                print(f"⚠️  无法修改硬件加速设置: {e}")
                
        except ImportError:
            print("⚠️  无法访问注册表")
    
    def _is_black_screen(self, frame):
        """检测是否为黑屏"""
        if frame is None:
            return True
        
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        mean_brightness = np.mean(gray)
        return mean_brightness < 15
    
    def show_manual_solutions(self):
        """显示手动解决方案"""
        print("\n" + "=" * 60)
        print("🛠️  手动解决方案指南")
        print("=" * 60)
        
        print("\n📋 请尝试以下手动解决方案:")
        
        print("\n1. 播放器设置调整:")
        print("   • 禁用硬件加速/GPU加速")
        print("   • 使用窗口模式而非全屏模式")
        print("   • 更改视频渲染器（如从DirectX改为GDI）")
        print("   • 禁用DXVA/CUDA硬件解码")
        
        print("\n2. 系统设置调整:")
        print("   • 更新显卡驱动程序")
        print("   • 禁用Windows硬件加速GPU调度")
        print("   • 临时禁用Windows Defender实时保护")
        print("   • 以管理员身份运行录屏程序")
        
        print("\n3. 替代解决方案:")
        print("   • 尝试不同的播放器软件")
        print("   • 使用OBS Studio等专业录屏软件")
        print("   • 考虑使用外部采集卡")
        print("   • 在虚拟机中运行播放器")
        
        print("\n4. 特定播放器解决方案:")
        print("   • VLC: 设置->视频->输出->GDI视频输出")
        print("   • PotPlayer: 设置->视频->渲染器->系统内存")
        print("   • 浏览器: 禁用硬件加速，使用Firefox")
        
        print(f"\n📄 详细解决方案请查看: 播放器黑屏解决方案.md")
    
    def test_solution(self):
        """测试解决方案效果"""
        if not self.successful_solution:
            print("❌ 没有找到成功的解决方案")
            return False
        
        print(f"\n🧪 测试解决方案: {self.successful_solution}")
        print("-" * 40)
        
        # 进行录制测试
        self.recorder.enable_drm_bypass(True)
        self.recorder.set_fps(15)
        
        print("开始5秒录制测试...")
        
        if self.recorder.start_recording("black_screen_solution_test.avi", duration=5):
            print("✅ 录制测试开始")
            time.sleep(6)
            
            if os.path.exists("black_screen_solution_test.avi"):
                file_size = os.path.getsize("black_screen_solution_test.avi")
                print(f"✅ 录制测试完成 - 文件大小: {file_size} 字节")
                
                # 验证录制内容
                cap = cv2.VideoCapture("black_screen_solution_test.avi")
                ret, frame = cap.read()
                cap.release()
                
                if ret and not self._is_black_screen(frame):
                    print("✅ 录制内容验证成功 - 没有黑屏问题")
                    return True
                else:
                    print("⚠️  录制内容可能仍有黑屏问题")
                    return False
            else:
                print("❌ 录制文件未生成")
                return False
        else:
            print("❌ 录制测试失败")
            return False
    
    def generate_report(self):
        """生成解决报告"""
        print("\n" + "=" * 60)
        print("📊 解决方案报告")
        print("=" * 60)
        
        print(f"\n尝试的解决方案数量: {len(self.solutions_tried)}")
        print(f"成功的解决方案: {self.successful_solution or '无'}")
        
        if self.successful_solution:
            print(f"\n✅ 推荐使用解决方案: {self.successful_solution}")
            print("   您可以在录屏程序中启用对应的功能")
        else:
            print(f"\n❌ 自动解决失败，请参考手动解决方案")
        
        print(f"\n📁 生成的测试文件:")
        test_files = [
            "solution_standard_drm.png",
            "solution_advanced_drm.png", 
            "solution_gpu_buffer.png",
            "solution_multi_process.png",
            "solution_window_composition.png",
            "solution_system_optimization.png",
            "black_screen_solution_test.avi"
        ]
        
        for file in test_files:
            if os.path.exists(file):
                print(f"   ✅ {file}")
            else:
                print(f"   ❌ {file} (未生成)")

def main():
    """主函数"""
    print("🎯 播放器黑屏问题一键解决工具 v2.0")
    print("专门解决视频播放器的反录屏黑屏问题")
    
    # 检查是否有播放器在运行
    print("\n⚠️  使用提示:")
    print("1. 请先打开要录制的视频播放器")
    print("2. 开始播放视频内容")
    print("3. 确保播放器窗口可见")
    print("4. 建议以管理员身份运行此程序")
    
    input("\n按回车键开始自动解决...")
    
    solver = BlackScreenSolver()
    
    try:
        # 尝试解决黑屏问题
        success = solver.solve_black_screen()
        
        if success:
            # 测试解决方案
            solver.test_solution()
        
        # 生成报告
        solver.generate_report()
        
        if success:
            print(f"\n🎉 恭喜！黑屏问题已解决")
            print(f"   成功方案: {solver.successful_solution}")
            print(f"   现在可以正常录制视频内容了")
        else:
            print(f"\n😔 自动解决失败，请查看手动解决方案")
            
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"\n\n程序运行出错: {e}")
        print("请检查系统环境和权限设置")

if __name__ == "__main__":
    main()
