#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
带进程伪装的录屏程序启动器
自动启用所有反检测功能
"""

import os
import sys
import time
import logging
from screen_recorder import AntiDetectionRecorder

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def show_banner():
    """显示程序横幅"""
    print("="*60)
    print("    高级反检测录屏程序 v2.0")
    print("    支持进程伪装和DRM绕过")
    print("="*60)
    print()

def show_disguise_info(recorder):
    """显示伪装信息"""
    try:
        if hasattr(recorder, 'anti_detection'):
            disguise_status = recorder.anti_detection.get_disguise_status()
            if disguise_status:
                print("🎭 进程伪装信息:")
                print(f"   伪装名称: {disguise_status['name']}")
                print(f"   伪装路径: {disguise_status['path']}")
                print(f"   伪装命令行: {disguise_status['cmdline'][:50]}...")
                print(f"   假进程数量: {len(disguise_status['fake_processes'])}")
                print()
            else:
                print("⚠️  进程伪装未启用")
                print()
    except Exception as e:
        logger.warning(f"获取伪装信息失败: {e}")

def show_help():
    """显示帮助信息"""
    print("📖 可用命令:")
    print("   start <文件名>     - 开始录制")
    print("   stop              - 停止录制")
    print("   status            - 显示状态")
    print("   disguise          - 显示伪装信息")
    print("   method <方法>     - 设置录制方法")
    print("   fps <帧率>        - 设置帧率")
    print("   help              - 显示帮助")
    print("   quit              - 退出程序")
    print()
    print("🎯 录制方法:")
    print("   mss               - MSS截屏 (默认)")
    print("   pyautogui         - PyAutoGUI截屏")
    print("   drm_bypass        - DRM绕过截屏")
    print("   advanced_drm      - 高级DRM绕过")
    print("   dxgi              - DXGI桌面复制")
    print()

def main():
    """主函数"""
    show_banner()
    
    try:
        # 创建录屏器实例
        print("🚀 初始化反检测录屏器...")
        recorder = AntiDetectionRecorder()
        
        # 等待初始化完成
        time.sleep(2)
        
        # 显示伪装信息
        show_disguise_info(recorder)
        
        # 显示帮助
        show_help()
        
        print("✅ 录屏器已就绪，输入命令开始使用")
        print("💡 提示: 输入 'help' 查看所有命令")
        print("-" * 60)
        
        # 主命令循环
        while True:
            try:
                command = input("录屏器> ").strip().lower()
                
                if not command:
                    continue
                
                cmd = command.split()
                
                if cmd[0] == "start":
                    if len(cmd) > 1:
                        filename = cmd[1]
                        if not filename.endswith('.avi'):
                            filename += '.avi'
                        
                        print(f"🎬 开始录制: {filename}")
                        recorder.start_recording(filename)
                        
                        # 显示录制状态
                        time.sleep(1)
                        if recorder.is_recording():
                            print(f"✅ 录制已开始")
                            print(f"   文件: {filename}")
                            print(f"   方法: {recorder.method}")
                            print(f"   帧率: {recorder.fps} FPS")
                            print(f"   DRM绕过: {'启用' if recorder.drm_bypass_enabled else '禁用'}")
                        else:
                            print("❌ 录制启动失败")
                    else:
                        print("❌ 请指定输出文件名")
                
                elif cmd[0] == "stop":
                    if recorder.is_recording():
                        print("🛑 停止录制...")
                        recorder.stop_recording()
                        print("✅ 录制已停止")
                    else:
                        print("⚠️  当前没有在录制")
                
                elif cmd[0] == "status":
                    status = "🔴 录制中" if recorder.is_recording() else "⚪ 未录制"
                    print(f"状态: {status}")
                    print(f"方法: {recorder.method}")
                    print(f"帧率: {recorder.fps} FPS")
                    print(f"隐身模式: {'启用' if recorder.stealth_mode else '禁用'}")
                    print(f"DRM绕过: {'启用' if recorder.drm_bypass_enabled else '禁用'}")
                
                elif cmd[0] == "disguise":
                    show_disguise_info(recorder)
                
                elif cmd[0] == "method":
                    if len(cmd) > 1:
                        method = cmd[1]
                        valid_methods = ['mss', 'pyautogui', 'drm_bypass', 'advanced_drm', 'dxgi']
                        if method in valid_methods:
                            recorder.set_method(method)
                            print(f"✅ 录制方法设置为: {method}")
                        else:
                            print(f"❌ 无效的方法: {method}")
                            print(f"   可用方法: {', '.join(valid_methods)}")
                    else:
                        print("❌ 请指定录制方法")
                
                elif cmd[0] == "fps":
                    if len(cmd) > 1:
                        try:
                            fps = int(cmd[1])
                            if 1 <= fps <= 60:
                                recorder.set_fps(fps)
                                print(f"✅ 帧率设置为: {fps} FPS")
                            else:
                                print("❌ 帧率必须在1-60之间")
                        except ValueError:
                            print("❌ 请输入有效的数字")
                    else:
                        print("❌ 请指定帧率")
                
                elif cmd[0] == "help":
                    show_help()
                
                elif cmd[0] == "quit" or cmd[0] == "exit":
                    if recorder.is_recording():
                        print("🛑 停止录制...")
                        recorder.stop_recording()
                    print("👋 正在退出...")
                    break
                
                else:
                    print(f"❌ 未知命令: {cmd[0]}")
                    print("💡 输入 'help' 查看可用命令")
            
            except EOFError:
                # Ctrl+D
                break
            except KeyboardInterrupt:
                # Ctrl+C
                print("\n🛑 收到中断信号...")
                break
    
    except Exception as e:
        logger.error(f"程序运行出错: {e}")
        print(f"❌ 程序出错: {e}")
    
    finally:
        # 确保清理资源
        try:
            if 'recorder' in locals():
                print("🧹 清理资源...")
                recorder.cleanup()
        except Exception as e:
            logger.error(f"资源清理失败: {e}")
    
    print("👋 程序已退出")

if __name__ == "__main__":
    main()
